<template>
  <c-modal
    v-model:open="open"
    :title="`[${deptRecord?.deptName}] 推送管理`"
    width="80%"
    full-modal
    :confirm-loading="confirmLoading"
    :footer="null"
  >
    <div class="my-4">
      <PushUsers
        v-model:current-dept-id="currentDeptId"
        v-model:force="sendForm.force"
        hidden-edit-dept
        layout="horizontal"
        @data-change="handleUserDataChange"
      />
    </div>

    <div class="w-100% flex gap4">
      <div class="w50%">
        <a-tabs v-model:active-key="activeTab" tab-position="top">
          <a-tab-pane
            :key="DeptPushType.疑似舆情"
            tab="疑似舆情"
          >
            <div class="space-x-4">
              <a-button
                type="primary"
                :loading="confirmLoading"
                @click="pushSuspiciousOpinion"
              >
                推送疑似舆情
              </a-button>
            </div>
          </a-tab-pane>

          <a-tab-pane
            :key="DeptPushType.事件"
            tab="事件"
          >
            <div class="space-x-4">
              <a-button
                type="primary"
                :loading="confirmLoading"
                @click="pushEvent"
              >
                推送事件
              </a-button>
            </div>
          </a-tab-pane>

          <a-tab-pane
            :key="DeptPushType.舆情日报"
            tab="舆情日报"
          >
            <div class="space-x-4">
              <a-button
                type="primary"
                :loading="confirmLoading"
                @click="pushDailyReport"
              >
                日报推送
              </a-button>
              <a-button
                type="primary"
                ghost
                @click="useExport.exportDailyNewspaper"
              >
                导出日报
              </a-button>
            </div>
          </a-tab-pane>

          <a-tab-pane
            :key="DeptPushType.专题报告"
            tab="专题报告"
          >
            <div class="space-x-4">
              <a-button
                type="primary"
                :loading="confirmLoading"
                @click="pushSpecialReport"
              >
                推送专题报告
              </a-button>
            </div>
          </a-tab-pane>

          <a-tab-pane
            :key="DeptPushType.舆情月报"
            tab="舆情月报"
          >
            <div class="space-x-4">
              <a-button
                type="primary"
                :loading="confirmLoading"
                @click="pushMonthlyReport"
              >
                月报推送
              </a-button>
              <a-button
                type="primary"
                ghost
                @click="useExport.exportBriefing"
              >
                导出月报
              </a-button>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>

      <PushRecords
        ref="pushRecordsRef"
        class="w0 flex-1"
        :current-dept-id="currentDeptId"
        :entity-id="entityId"
        :entity-type="entityType"
      />
    </div>
  </c-modal>
</template>

<script lang="ts" setup>
import type { DeptPushChannel, PushTarget, UserViewModel } from '@/api/models'
import * as api from '@/api'
import { DeptPushType } from '@/api/models'
import PushRecords from '@/components/PushRecords.vue'
import PushUsers from '@/components/PushUsers.vue'
import { message } from 'ant-design-vue'

interface DeptModel {
  deptName: string
  deptId: GUID
}

const props = defineProps<{
  deptRecord?: DeptModel
}>()

const emit = defineEmits<{
  (e: 'success'): void
}>()

const useExport = useExportHook()

const open = defineModel<boolean>('open', {
  required: true,
  default: false,
})

const confirmLoading = ref(false)
const activeTab = ref<DeptPushType>(DeptPushType.疑似舆情)

// 推送相关数据
const sendForm = ref({
  force: false,
})

const userData = ref<UserViewModel[]>([])
const channelMap = ref<Record<string, DeptPushChannel[]>>({})

// 子组件引用
const pushRecordsRef = ref<InstanceType<typeof PushRecords>>()

// 计算属性
const currentDeptId = computed(() => props.deptRecord?.deptId || Guid.empty)
const entityId = computed(() => Guid.empty) // 根据实际需求设置
const entityType = computed(() => activeTab.value)

// 处理用户数据变化
function handleUserDataChange(data: { userData: UserViewModel[], channelMap: Record<string, DeptPushChannel[]> }) {
  userData.value = data.userData
  channelMap.value = data.channelMap
}

// 获取提交数据
function getSubmitData(): PushTarget[] {
  return userData.value.map(user => ({
    pushUserId: user.id as string,
    channels: channelMap.value[user.id!] || [],
  }))
}

// 刷新推送记录
function refreshPushRecords() {
  pushRecordsRef.value?.refresh()
}

// 通用推送函数
async function pushContent(pushType: DeptPushType, typeName: string) {
  if (!props.deptRecord) {
    message.error('请选择推送单位')
    return
  }

  if (userData.value.length === 0) {
    message.error('请先选择推送用户')
    return
  }

  try {
    confirmLoading.value = true
    const submitData = getSubmitData()

    await api.DeptPushLogs.Send_PostAsync({
      entityId: entityId.value,
      entityType: pushType,
      force: sendForm.value.force,
    }, submitData)

    message.success(`${typeName}推送成功`)
    refreshPushRecords()
    emit('success')
  }
  catch (error: any) {
    console.error('推送失败:', error)
    message.error(error.message || '推送失败')
  }
  finally {
    confirmLoading.value = false
  }
}

// 推送疑似舆情
async function pushSuspiciousOpinion() {
  await pushContent(DeptPushType.疑似舆情, '疑似舆情')
}

// 推送事件
async function pushEvent() {
  await pushContent(DeptPushType.事件, '事件')
}

// 推送日报
async function pushDailyReport() {
  await pushContent(DeptPushType.舆情日报, '日报')
}

// 推送专题报告
async function pushSpecialReport() {
  await pushContent(DeptPushType.专题报告, '专题报告')
}

// 推送月报
async function pushMonthlyReport() {
  await pushContent(DeptPushType.舆情月报, '月报')
}

// 监听对话框打开状态，重置表单
watch(() => open.value, (newVal) => {
  if (newVal) {
    activeTab.value = DeptPushType.疑似舆情
    sendForm.value.force = false
    userData.value = []
    channelMap.value = {}
  }
})

/** 导出 */
function useExportHook() {
  function exportDailyNewspaper() {
    useDownload(() => api.OpinionManage.ExportDailyNewspaperWord_PostAsync({ }))
  }

  function exportBriefing() {
    useDownload(() => api.OpinionManage.ExportSimpleDailyReport_GetAsync({ }))
  }

  return { exportDailyNewspaper, exportBriefing }
}
</script>

<style scoped lang="less">
:deep(.ant-tabs-content-holder) {
  min-height: 300px;
}
</style>
